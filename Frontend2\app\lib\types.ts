// Type definitions for the LawVriksh application

export type UserType = 'USER' | 'Creator';

export type PopupType = 'form' | 'thankyou' | 'features' | 'feedback' | 'viewmore' | 'notinterested';

export interface RegistrationFormData {
  name: string;
  email: string;
  phone: string;
  gender: string;
  profession: string;
  whyInterested: string;
}

export interface NotInterestedFormData {
  name: string;
  email: string;
  whyNotInterested: string;
}

export interface FeedbackFormData {
  visualDesign: string;
  easeOfNavigation: string;
  mobileResponsiveness: string;
  overallSatisfaction: string;
  easeOfTasks: string;
  qualityOfServices: string;
  likeMost: string;
  improvements: string;
  features: string;
  legalChallenges: string;
  additionalComments: string;
  contactWilling: string;
  contactEmail: string;
  // Conditional fields for low ratings
  visualDesignIssue: string;
  easeOfNavigationIssue: string;
  mobileResponsivenessIssue: string;
  overallSatisfactionIssue: string;
  easeOfTasksIssue: string;
  qualityOfServicesIssue: string;
}

export interface AdminCredentials {
  username: string;
  password: string;
}

// Component Props Interfaces
export interface HeaderProps {
  onViewMoreClick: () => void;
}

export interface PopupProps {
  isOpen: boolean;
  onClose: () => void;
  type: PopupType;
  userType?: UserType;
  isNotInterested?: boolean;
  feedbackSubmitted?: boolean;
  onUserTypeClick?: (type: UserType) => void;
  onNotInterestedClick?: () => void;
  onFeaturesClick?: () => void;
  onFeedbackClick?: () => void;
  onAdminClick?: () => void;
}

export interface FormComponentProps {
  onSubmit: (e: React.FormEvent) => void;
  isSubmitting: boolean;
  submitError: string;
}

export interface RegistrationFormProps extends FormComponentProps {
  formData: RegistrationFormData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  userType: UserType;
}

export interface NotInterestedFormProps extends FormComponentProps {
  formData: NotInterestedFormData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export interface FeedbackFormProps extends FormComponentProps {
  formData: FeedbackFormData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export interface AdminLoginProps {
  onLogin: () => void;
}

export interface AdminDashboardProps {
  onLogout: () => void;
}

// Feature data interface
export interface Feature {
  title: string;
  description: string;
}

// Constants
export const FEATURES: Feature[] = [
  {
    title: "Write Faster, Publish Smarter",
    description: "Create polished legal blogs in minutes with AI-powered tools."
  },
  {
    title: "Simplify Complex Law",
    description: "Turn dense legal topics into clear, authoritative articles effortlessly."
  },
  {
    title: "Listen on the Go",
    description: "Absorb expert insights and updates hands-free with audio summaries."
  },
  {
    title: "Monetize Your Expertise",
    description: "Build an audience and earn rewards by sharing your legal knowledge."
  }
];

export const GENDER_OPTIONS = [
  { value: '', label: 'Select Gender' },
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer-not-to-say', label: 'Prefer not to say' }
];

export const CONTACT_PREFERENCES = [
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'either', label: 'Either Email or Phone' }
];

export const RATING_SCALE = [1, 2, 3, 4, 5];
