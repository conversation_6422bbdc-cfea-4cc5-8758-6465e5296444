import { POPUP_STYLES } from "~/lib/utils";

interface NotInterestedFormProps {
  onClose: () => void;
}

export function NotInterestedForm({ onClose }: NotInterestedFormProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Placeholder - no actual form logic as requested
    console.log('Not interested form submitted');
    // Show thank you message or close popup
    onClose();
  };

  return (
    <div className="w-full">
      <h2 className={POPUP_STYLES.title}>
        We'd Love to Know More
      </h2>
      
      <form onSubmit={handleSubmit} className={POPUP_STYLES.form}>
        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="notInterestedName" className={POPUP_STYLES.label}>
            Name *
          </label>
          <input
            type="text"
            id="notInterestedName"
            name="name"
            className={POPUP_STYLES.input}
            placeholder="Enter your name"
            required
          />
        </div>

        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="notInterestedEmail" className={POPUP_STYLES.label}>
            Email *
          </label>
          <input
            type="email"
            id="notInterestedEmail"
            name="email"
            className={POPUP_STYLES.input}
            placeholder="Enter your email"
            required
          />
        </div>

        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="whyNotInterested" className={POPUP_STYLES.label}>
            Why are you not interested? (Optional)
          </label>
          <textarea
            id="whyNotInterested"
            name="whyNotInterested"
            rows={3}
            className={POPUP_STYLES.textarea}
            placeholder="Help us understand what we could improve..."
          />
        </div>

        <button 
          type="submit" 
          className={POPUP_STYLES.button}
        >
          Submit Response
        </button>
      </form>
    </div>
  );
}

export default NotInterestedForm;
