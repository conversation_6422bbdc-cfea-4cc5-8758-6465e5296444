import type { UserType } from "~/lib/types";
import { POPUP_STYLES } from "~/lib/utils";

interface RegistrationFormProps {
  userType: UserType;
  onClose: () => void;
}

export function RegistrationForm({ userType, onClose }: RegistrationFormProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Placeholder - no actual form logic as requested
    console.log('Registration form submitted for:', userType);
    // Show thank you message or close popup
    onClose();
  };

  return (
    <div className="w-full">
      <h2 className={POPUP_STYLES.title}>
        Join as {userType}
      </h2>
      
      <form onSubmit={handleSubmit} className={POPUP_STYLES.form}>
        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="name" className={POPUP_STYLES.label}>
            Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            className={POPUP_STYLES.input}
            placeholder="Enter your name"
            required
          />
        </div>

        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="email" className={POPUP_STYLES.label}>
            Email *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            className={POPUP_STYLES.input}
            placeholder="Enter your email"
            required
          />
        </div>

        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="phone" className={POPUP_STYLES.label}>
            Phone Number *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            className={POPUP_STYLES.input}
            placeholder="Enter your phone number"
            required
          />
        </div>

        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="gender" className={POPUP_STYLES.label}>
            Gender
          </label>
          <select
            id="gender"
            name="gender"
            className={POPUP_STYLES.select}
          >
            <option value="">Select Gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
            <option value="prefer-not-to-say">Prefer not to say</option>
          </select>
        </div>

        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="profession" className={POPUP_STYLES.label}>
            Profession
          </label>
          <input
            type="text"
            id="profession"
            name="profession"
            className={POPUP_STYLES.input}
            placeholder="e.g., Student, Lawyer, etc."
          />
        </div>

        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="whyInterested" className={POPUP_STYLES.label}>
            Why are you interested? (Optional)
          </label>
          <textarea
            id="whyInterested"
            name="whyInterested"
            rows={3}
            className={POPUP_STYLES.textarea}
            placeholder="Tell us what interests you about LawVriksh..."
          />
        </div>

        <button 
          type="submit" 
          className={POPUP_STYLES.button}
        >
          Join Waiting List
        </button>
      </form>
    </div>
  );
}

export default RegistrationForm;
