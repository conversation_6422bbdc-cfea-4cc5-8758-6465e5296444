import { useState } from "react";
import type { MetaFunction } from "@remix-run/node";
import { useNavigate } from "@remix-run/react";
import AdminLogin from "~/components/AdminLogin";
import AdminDashboard from "~/components/AdminDashboard";

export const meta: MetaFunction = () => {
  return [
    { title: "LawVriksh Admin - Dashboard" },
    { name: "description", content: "LawVriksh Admin Dashboard - Manage user registrations and feedback" },
    { name: "robots", content: "noindex, nofollow" }, // Prevent search engine indexing
  ];
};

export default function Admin() {
  const navigate = useNavigate();
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(false);

  const handleAdminLogin = () => {
    setIsAdminLoggedIn(true);
  };

  const handleAdminLogout = () => {
    setIsAdminLoggedIn(false);
    // Optionally redirect to home page
    // navigate('/');
  };

  return (
    <>
      {isAdminLoggedIn ? (
        <AdminDashboard onLogout={handleAdminLogout} />
      ) : (
        <AdminLogin onLogin={handleAdminLogin} />
      )}
    </>
  );
}
