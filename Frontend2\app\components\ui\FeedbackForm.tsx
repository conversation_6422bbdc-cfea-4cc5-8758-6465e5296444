import { POPUP_STYLES, RATING_SCALE } from "~/lib/utils";

interface FeedbackFormProps {
  onClose: () => void;
}

export function FeedbackForm({ onClose }: FeedbackFormProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Placeholder - no actual form logic as requested
    console.log('Feedback form submitted');
    // Show thank you message or close popup
    onClose();
  };

  const renderRatingQuestion = (
    name: string,
    label: string,
    questionNumber: number
  ) => (
    <div className="mb-5">
      <label className="block font-medium text-sm text-gray-800 mb-3">
        {questionNumber}. {label}
      </label>
      <div className="flex gap-4 flex-wrap">
        {RATING_SCALE.map(num => (
          <label key={num} className="flex items-center gap-1 cursor-pointer text-sm text-gray-600">
            <input
              type="radio"
              name={name}
              value={num.toString()}
              className="w-4 h-4 accent-law-gold"
            />
            <span className="font-medium">{num}</span>
          </label>
        ))}
      </div>
    </div>
  );

  return (
    <div className="w-full">
      <h2 className={POPUP_STYLES.title}>
        Help us improve your experience
      </h2>
      
      <p className="text-center text-gray-600 text-sm mb-3">
        <em>Estimated time: 5-7 minutes</em>
      </p>
      
      <p className="text-sm text-gray-600 mb-8 text-center leading-relaxed">
        Thank you for taking the time to share your feedback. Your input helps us enhance our website to better serve your legal needs. All responses are confidential and used solely to improve our platform.
      </p>

      <form onSubmit={handleSubmit} className="flex flex-col gap-8">
        {/* User Interface Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-3 bg-cover bg-center">
            User Interface
          </h3>
          <p className="text-sm text-gray-600 mb-5">
            Please rate the following on a scale of 1 to 5 (1 = Poor, 5 = Excellent):
          </p>

          {renderRatingQuestion('visualDesign', 'Visual design and layout', 1)}
          {renderRatingQuestion('easeOfNavigation', 'Ease of navigation', 2)}
          {renderRatingQuestion('mobileResponsiveness', 'Mobile responsiveness', 3)}
        </div>

        {/* User Experience Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-3 bg-cover bg-center">
            User Experience
          </h3>
          <p className="text-sm text-gray-600 mb-5">
            Please rate the following on a scale of 1 to 5 (1 = Poor, 5 = Excellent):
          </p>

          {renderRatingQuestion('overallSatisfaction', 'Overall satisfaction with the website', 4)}
          {renderRatingQuestion('easeOfTasks', 'Ease of completing tasks (e.g., finding information, using tools)', 5)}
          {renderRatingQuestion('qualityOfServices', 'Quality of services provided', 6)}
        </div>

        {/* Suggestions Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-5 bg-cover bg-center">
            Suggestions and Legal Needs
          </h3>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="likeMost" className={POPUP_STYLES.label}>
              7. What do you like most about our website?
            </label>
            <textarea
              id="likeMost"
              name="likeMost"
              rows={3}
              className={POPUP_STYLES.textarea}
            />
          </div>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="improvements" className={POPUP_STYLES.label}>
              8. What improvements would you suggest for our website?
            </label>
            <textarea
              id="improvements"
              name="improvements"
              rows={3}
              className={POPUP_STYLES.textarea}
            />
          </div>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="features" className={POPUP_STYLES.label}>
              9. Are there any features you would like us to add?
            </label>
            <textarea
              id="features"
              name="features"
              rows={3}
              className={POPUP_STYLES.textarea}
            />
          </div>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="legalChallenges" className={POPUP_STYLES.label}>
              10. What legal challenges are you facing that our website could help address?
            </label>
            <textarea
              id="legalChallenges"
              name="legalChallenges"
              rows={3}
              className={POPUP_STYLES.textarea}
            />
          </div>
        </div>

        {/* Additional Comments Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-5 bg-cover bg-center">
            Additional Comments
          </h3>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="additionalComments" className={POPUP_STYLES.label}>
              11. Is there anything else you would like to share?
            </label>
            <textarea
              id="additionalComments"
              name="additionalComments"
              rows={3}
              className={POPUP_STYLES.textarea}
            />
          </div>
        </div>

        {/* Contact Information Section */}
        <div>
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-5 bg-cover bg-center">
            Contact Information
          </h3>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="contactEmail" className={POPUP_STYLES.label}>
              12. Email address (required for feedback submission):
            </label>
            <input
              type="email"
              id="contactEmail"
              name="contactEmail"
              className={POPUP_STYLES.input}
              placeholder="Enter your email address"
              required
            />
          </div>

          <div className={POPUP_STYLES.formGroup}>
            <label className="block font-medium text-base text-gray-800 mb-3">
              13. How would you prefer to be contacted for follow-up? (required)
            </label>
            <div className="flex gap-5 mt-3">
              <label className="flex items-center gap-1 cursor-pointer text-sm text-gray-600">
                <input
                  type="radio"
                  name="contactWilling"
                  value="email"
                  className="w-4 h-4 accent-law-gold"
                  required
                />
                <span>Email</span>
              </label>
              <label className="flex items-center gap-1 cursor-pointer text-sm text-gray-600">
                <input
                  type="radio"
                  name="contactWilling"
                  value="phone"
                  className="w-4 h-4 accent-law-gold"
                  required
                />
                <span>Phone</span>
              </label>
              <label className="flex items-center gap-1 cursor-pointer text-sm text-gray-600">
                <input
                  type="radio"
                  name="contactWilling"
                  value="either"
                  className="w-4 h-4 accent-law-gold"
                  required
                />
                <span>Either Email or Phone</span>
              </label>
            </div>
          </div>
        </div>

        <button 
          type="submit" 
          className={POPUP_STYLES.button}
        >
          Submit Feedback
        </button>
      </form>
    </div>
  );
}

export default FeedbackForm;
